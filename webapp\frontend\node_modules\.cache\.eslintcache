[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "70", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "71", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "74", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "81", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "93"}, {"size": 557, "mtime": 1746952718482, "results": "94", "hashOfConfig": "95"}, {"size": 2728, "mtime": 1746952740200, "results": "96", "hashOfConfig": "95"}, {"size": 996, "mtime": 1746970152489, "results": "97", "hashOfConfig": "95"}, {"size": 10788, "mtime": 1746864244183, "results": "98", "hashOfConfig": "95"}, {"size": 18405, "mtime": 1748034045686, "results": "99", "hashOfConfig": "95"}, {"size": 6001, "mtime": 1748200429907, "results": "100", "hashOfConfig": "95"}, {"size": 2216, "mtime": 1746640055487, "results": "101", "hashOfConfig": "95"}, {"size": 7394, "mtime": 1748034003517, "results": "102", "hashOfConfig": "95"}, {"size": 6749, "mtime": 1746282201800, "results": "103", "hashOfConfig": "95"}, {"size": 21982, "mtime": 1748068137555, "results": "104", "hashOfConfig": "95"}, {"size": 2535, "mtime": 1746647873596, "results": "105", "hashOfConfig": "95"}, {"size": 2050, "mtime": 1746647945415, "results": "106", "hashOfConfig": "95"}, {"size": 700, "mtime": 1747545501078, "results": "107", "hashOfConfig": "95"}, {"size": 17518, "mtime": 1748664526035, "results": "108", "hashOfConfig": "95"}, {"size": 3999, "mtime": 1746943038491, "results": "109", "hashOfConfig": "95"}, {"size": 4025, "mtime": 1748693430883, "results": "110", "hashOfConfig": "95"}, {"size": 1630, "mtime": 1746336079554, "results": "111", "hashOfConfig": "95"}, {"size": 3070, "mtime": 1746637986362, "results": "112", "hashOfConfig": "95"}, {"size": 37999, "mtime": 1748666433118, "results": "113", "hashOfConfig": "95"}, {"size": 324, "mtime": 1748691711212, "results": "114", "hashOfConfig": "95"}, {"size": 9068, "mtime": 1746856425683, "results": "115", "hashOfConfig": "95"}, {"size": 2210, "mtime": 1747432283057, "results": "116", "hashOfConfig": "95"}, {"size": 4494, "mtime": 1748121063631, "results": "117", "hashOfConfig": "95"}, {"size": 37751, "mtime": 1748686911338, "results": "118", "hashOfConfig": "95"}, {"size": 3969, "mtime": 1746864496870, "results": "119", "hashOfConfig": "95"}, {"size": 3609, "mtime": 1746944025177, "results": "120", "hashOfConfig": "95"}, {"size": 4142, "mtime": 1746942978805, "results": "121", "hashOfConfig": "95"}, {"size": 3986, "mtime": 1746864510624, "results": "122", "hashOfConfig": "95"}, {"size": 3973, "mtime": 1746864489032, "results": "123", "hashOfConfig": "95"}, {"size": 2975, "mtime": 1747554796402, "results": "124", "hashOfConfig": "95"}, {"size": 3429, "mtime": 1747721794176, "results": "125", "hashOfConfig": "95"}, {"size": 3109, "mtime": 1747824114392, "results": "126", "hashOfConfig": "95"}, {"size": 2929, "mtime": 1747655572696, "results": "127", "hashOfConfig": "95"}, {"size": 3302, "mtime": 1748000902435, "results": "128", "hashOfConfig": "95"}, {"size": 5597, "mtime": 1748070089791, "results": "129", "hashOfConfig": "95"}, {"size": 5880, "mtime": 1748121404574, "results": "130", "hashOfConfig": "95"}, {"size": 3889, "mtime": 1748664890350, "results": "131", "hashOfConfig": "95"}, {"size": 4720, "mtime": 1746771178920, "results": "132", "hashOfConfig": "95"}, {"size": 7121, "mtime": 1746281148395, "results": "133", "hashOfConfig": "95"}, {"size": 7958, "mtime": 1746280443400, "results": "134", "hashOfConfig": "95"}, {"size": 6259, "mtime": 1746965906057, "results": "135", "hashOfConfig": "95"}, {"size": 4215, "mtime": 1746278746358, "results": "136", "hashOfConfig": "95"}, {"size": 1273, "mtime": 1746809069006, "results": "137", "hashOfConfig": "95"}, {"size": 14270, "mtime": 1748371983481, "results": "138", "hashOfConfig": "95"}, {"size": 2752, "mtime": 1747022186740, "results": "139", "hashOfConfig": "95"}, {"size": 1072, "mtime": 1746637929350, "results": "140", "hashOfConfig": "95"}, {"size": 6745, "mtime": 1747545492454, "results": "141", "hashOfConfig": "95"}, {"size": 38569, "mtime": 1748371531457, "results": "142", "hashOfConfig": "95"}, {"size": 23333, "mtime": 1746463652843, "results": "143", "hashOfConfig": "95"}, {"size": 47271, "mtime": 1748072224692, "results": "144", "hashOfConfig": "95"}, {"size": 38669, "mtime": 1748199713253, "results": "145", "hashOfConfig": "95"}, {"size": 1947, "mtime": 1748120984640, "results": "146", "hashOfConfig": "95"}, {"size": 54895, "mtime": 1748370360136, "results": "147", "hashOfConfig": "95"}, {"size": 14635, "mtime": 1748666301849, "results": "148", "hashOfConfig": "95"}, {"size": 8538, "mtime": 1748666450738, "results": "149", "hashOfConfig": "95"}, {"size": 11771, "mtime": 1746948731812, "results": "150", "hashOfConfig": "95"}, {"size": 2211, "mtime": 1748686293878, "results": "151", "hashOfConfig": "95"}, {"size": 9215, "mtime": 1748668814050, "results": "152", "hashOfConfig": "95"}, {"size": 10993, "mtime": 1747154871546, "results": "153", "hashOfConfig": "95"}, {"size": 12150, "mtime": 1748205557322, "results": "154", "hashOfConfig": "95"}, {"size": 24566, "mtime": 1748691444876, "results": "155", "hashOfConfig": "95"}, {"size": 7032, "mtime": 1748069273238, "results": "156", "hashOfConfig": "95"}, {"size": 8589, "mtime": 1748207111023, "results": "157", "hashOfConfig": "95"}, {"size": 9979, "mtime": 1748069243848, "results": "158", "hashOfConfig": "95"}, {"size": 10821, "mtime": 1748069202177, "results": "159", "hashOfConfig": "95"}, {"size": 36555, "mtime": 1747684003188, "results": "160", "hashOfConfig": "95"}, {"size": 9483, "mtime": 1747194869458, "results": "161", "hashOfConfig": "95"}, {"size": 13900, "mtime": 1748182219170, "results": "162", "hashOfConfig": "95"}, {"size": 48588, "mtime": 1747948123233, "results": "163", "hashOfConfig": "95"}, {"size": 92270, "mtime": 1748123070273, "results": "164", "hashOfConfig": "95"}, {"size": 522, "mtime": 1747022186711, "results": "165", "hashOfConfig": "95"}, {"size": 6612, "mtime": 1748069456201, "results": "166", "hashOfConfig": "95"}, {"size": 3796, "mtime": 1747022186720, "results": "167", "hashOfConfig": "95"}, {"size": 1703, "mtime": 1746972529152, "results": "168", "hashOfConfig": "95"}, {"size": 19892, "mtime": 1747554544219, "results": "169", "hashOfConfig": "95"}, {"size": 12050, "mtime": 1747547543421, "results": "170", "hashOfConfig": "95"}, {"size": 1686, "mtime": 1746946499500, "results": "171", "hashOfConfig": "95"}, {"size": 5145, "mtime": 1746914029633, "results": "172", "hashOfConfig": "95"}, {"size": 9788, "mtime": 1747491601484, "results": "173", "hashOfConfig": "95"}, {"size": 22179, "mtime": 1747432554979, "results": "174", "hashOfConfig": "95"}, {"size": 2258, "mtime": 1746946368534, "results": "175", "hashOfConfig": "95"}, {"size": 4094, "mtime": 1748161663641, "results": "176", "hashOfConfig": "95"}, {"size": 5273, "mtime": 1747946737459, "results": "177", "hashOfConfig": "95"}, {"size": 4346, "mtime": 1747491472989, "results": "178", "hashOfConfig": "95"}, {"size": 15571, "mtime": 1747980774491, "results": "179", "hashOfConfig": "95"}, {"size": 7839, "mtime": 1748664451344, "results": "180", "hashOfConfig": "95"}, {"size": 6529, "mtime": 1748664406267, "results": "181", "hashOfConfig": "95"}, {"size": 15739, "mtime": 1748664968476, "results": "182", "hashOfConfig": "95"}, {"size": 6448, "mtime": 1748664917658, "results": "183", "hashOfConfig": "95"}, {"size": 5536, "mtime": 1748670096009, "results": "184", "hashOfConfig": "95"}, {"size": 5457, "mtime": 1748666884369, "results": "185", "hashOfConfig": "95"}, {"size": 5605, "mtime": 1748666925194, "results": "186", "hashOfConfig": "95"}, {"size": 34116, "mtime": 1748694400985, "results": "187", "hashOfConfig": "95"}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["467", "468", "469", "470"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["471", "472", "473", "474", "475", "476", "477", "478", "479", "480"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["481"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["482", "483", "484", "485", "486", "487", "488", "489", "490", "491"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", ["507", "508", "509", "510"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["511"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["512", "513"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["529"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["530"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["531", "532"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["533", "534"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["535", "536"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["537", "538"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["539"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["540", "541", "542", "543", "544", "545", "546"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["547", "548", "549"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js", ["550", "551", "552", "553", "554", "555", "556"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["557", "558"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["559", "560"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["561", "562"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["563", "564"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["565", "566", "567", "568", "569", "570", "571", "572"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", ["573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["599", "600", "601", "602", "603", "604", "605"], ["606"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["630", "631"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["632", "633"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["645"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["668"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["669"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["670"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["682"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["683", "684", "685", "686"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["727", "728"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["729", "730"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["731", "732"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["753", "754", "755", "756", "757", "758", "759", "760"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["761"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["762", "763", "764"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["765"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["766"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["767"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["768"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["769", "770", "771", "772", "773", "774", "775", "776", "777"], [], {"ruleId": "778", "severity": 1, "message": "779", "line": 78, "column": 11, "nodeType": "780", "messageId": "781", "endLine": 78, "endColumn": 115}, {"ruleId": "778", "severity": 1, "message": "779", "line": 80, "column": 11, "nodeType": "780", "messageId": "781", "endLine": 80, "endColumn": 107}, {"ruleId": "778", "severity": 1, "message": "779", "line": 86, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 86, "endColumn": 105}, {"ruleId": "778", "severity": 1, "message": "779", "line": 89, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 89, "endColumn": 41}, {"ruleId": "782", "severity": 1, "message": "783", "line": 13, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 13, "endColumn": 9}, {"ruleId": "782", "severity": 1, "message": "786", "line": 20, "column": 25, "nodeType": "784", "messageId": "785", "endLine": 20, "endColumn": 34}, {"ruleId": "782", "severity": 1, "message": "787", "line": 21, "column": 19, "nodeType": "784", "messageId": "785", "endLine": 21, "endColumn": 35}, {"ruleId": "782", "severity": 1, "message": "788", "line": 22, "column": 12, "nodeType": "784", "messageId": "785", "endLine": 22, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "789", "line": 23, "column": 18, "nodeType": "784", "messageId": "785", "endLine": 23, "endColumn": 28}, {"ruleId": "782", "severity": 1, "message": "790", "line": 56, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 56, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "791", "line": 57, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 57, "endColumn": 23}, {"ruleId": "782", "severity": 1, "message": "792", "line": 58, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 58, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "793", "line": 59, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 59, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "794", "line": 68, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 68, "endColumn": 29}, {"ruleId": "782", "severity": 1, "message": "795", "line": 1, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "796", "line": 2, "column": 27, "nodeType": "784", "messageId": "785", "endLine": 2, "endColumn": 31}, {"ruleId": "782", "severity": 1, "message": "797", "line": 2, "column": 33, "nodeType": "784", "messageId": "785", "endLine": 2, "endColumn": 37}, {"ruleId": "782", "severity": 1, "message": "798", "line": 2, "column": 39, "nodeType": "784", "messageId": "785", "endLine": 2, "endColumn": 50}, {"ruleId": "782", "severity": 1, "message": "799", "line": 2, "column": 52, "nodeType": "784", "messageId": "785", "endLine": 2, "endColumn": 66}, {"ruleId": "782", "severity": 1, "message": "783", "line": 2, "column": 68, "nodeType": "784", "messageId": "785", "endLine": 2, "endColumn": 74}, {"ruleId": "782", "severity": 1, "message": "786", "line": 5, "column": 25, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 34}, {"ruleId": "782", "severity": 1, "message": "787", "line": 6, "column": 19, "nodeType": "784", "messageId": "785", "endLine": 6, "endColumn": 35}, {"ruleId": "782", "severity": 1, "message": "788", "line": 7, "column": 12, "nodeType": "784", "messageId": "785", "endLine": 7, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "789", "line": 8, "column": 18, "nodeType": "784", "messageId": "785", "endLine": 8, "endColumn": 28}, {"ruleId": "782", "severity": 1, "message": "800", "line": 43, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 43, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "797", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "798", "line": 10, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 10, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "801", "line": 11, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 11, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "796", "line": 12, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 12, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "802", "line": 13, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 13, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "803", "line": 18, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 18, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "804", "line": 19, "column": 15, "nodeType": "784", "messageId": "785", "endLine": 19, "endColumn": 27}, {"ruleId": "782", "severity": 1, "message": "805", "line": 20, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 20, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "806", "line": 21, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 21, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "807", "line": 22, "column": 13, "nodeType": "784", "messageId": "785", "endLine": 22, "endColumn": 23}, {"ruleId": "782", "severity": 1, "message": "808", "line": 23, "column": 14, "nodeType": "784", "messageId": "785", "endLine": 23, "endColumn": 25}, {"ruleId": "782", "severity": 1, "message": "809", "line": 28, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 28, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "810", "line": 31, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 31, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "811", "line": 51, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 51, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "812", "line": 56, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 56, "endColumn": 20}, {"ruleId": "782", "severity": 1, "message": "813", "line": 5, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "803", "line": 13, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 13, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "810", "line": 21, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 21, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "814", "line": 28, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 28, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "815", "line": 11, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 11, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "803", "line": 13, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 13, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "810", "line": 21, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 21, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "797", "line": 8, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 8, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "798", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "816", "line": 11, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 11, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "817", "line": 14, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 14, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "818", "line": 25, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 25, "endColumn": 16}, {"ruleId": "782", "severity": 1, "message": "819", "line": 31, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 31, "endColumn": 16}, {"ruleId": "782", "severity": 1, "message": "810", "line": 37, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 37, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "820", "line": 39, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 39, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "814", "line": 41, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 41, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "821", "line": 109, "column": 19, "nodeType": "784", "messageId": "785", "endLine": 109, "endColumn": 29}, {"ruleId": "782", "severity": 1, "message": "822", "line": 117, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 117, "endColumn": 28}, {"ruleId": "782", "severity": 1, "message": "823", "line": 118, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 118, "endColumn": 23}, {"ruleId": "782", "severity": 1, "message": "824", "line": 118, "column": 25, "nodeType": "784", "messageId": "785", "endLine": 118, "endColumn": 41}, {"ruleId": "825", "severity": 1, "message": "826", "line": 472, "column": 6, "nodeType": "827", "endLine": 472, "endColumn": 15, "suggestions": "828"}, {"ruleId": "782", "severity": 1, "message": "829", "line": 477, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 477, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "830", "line": 1, "column": 27, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 36}, {"ruleId": "782", "severity": 1, "message": "831", "line": 49, "column": 19, "nodeType": "784", "messageId": "785", "endLine": 49, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "802", "line": 15, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 15, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "832", "line": 39, "column": 14, "nodeType": "784", "messageId": "785", "endLine": 39, "endColumn": 25}, {"ruleId": "782", "severity": 1, "message": "813", "line": 5, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "814", "line": 31, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 31, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "813", "line": 5, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "814", "line": 31, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 31, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "813", "line": 5, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "814", "line": 31, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 31, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "814", "line": 27, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 27, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "813", "line": 5, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "833", "line": 6, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 6, "endColumn": 9}, {"ruleId": "782", "severity": 1, "message": "803", "line": 14, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 14, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "810", "line": 23, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 23, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "814", "line": 30, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 30, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "834", "line": 33, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 33, "endColumn": 29}, {"ruleId": "782", "severity": 1, "message": "835", "line": 38, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 38, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "810", "line": 20, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 20, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "814", "line": 27, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 27, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "835", "line": 35, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 35, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "813", "line": 5, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "833", "line": 6, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 6, "endColumn": 9}, {"ruleId": "782", "severity": 1, "message": "803", "line": 14, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 14, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "810", "line": 23, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 23, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "814", "line": 30, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 30, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "834", "line": 33, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 33, "endColumn": 29}, {"ruleId": "782", "severity": 1, "message": "835", "line": 38, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 38, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "810", "line": 24, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 24, "endColumn": 26}, {"ruleId": "825", "severity": 1, "message": "836", "line": 53, "column": 6, "nodeType": "827", "endLine": 53, "endColumn": 18, "suggestions": "837"}, {"ruleId": "782", "severity": 1, "message": "838", "line": 1, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "839", "line": 5, "column": 7, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "802", "line": 14, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 14, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "840", "line": 28, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 28, "endColumn": 18}, {"ruleId": "782", "severity": 1, "message": "838", "line": 1, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "839", "line": 5, "column": 7, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "797", "line": 8, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 8, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "798", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "801", "line": 10, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 10, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "841", "line": 23, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 23, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "842", "line": 24, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 24, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "804", "line": 46, "column": 15, "nodeType": "784", "messageId": "785", "endLine": 46, "endColumn": 27}, {"ruleId": "782", "severity": 1, "message": "843", "line": 47, "column": 12, "nodeType": "784", "messageId": "785", "endLine": 47, "endColumn": 21}, {"ruleId": "825", "severity": 1, "message": "844", "line": 134, "column": 6, "nodeType": "827", "endLine": 134, "endColumn": 18, "suggestions": "845"}, {"ruleId": "782", "severity": 1, "message": "797", "line": 8, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 8, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "798", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "801", "line": 10, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 10, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "841", "line": 23, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 23, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "842", "line": 24, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 24, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "802", "line": 25, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 25, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "805", "line": 37, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 37, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "846", "line": 41, "column": 13, "nodeType": "784", "messageId": "785", "endLine": 41, "endColumn": 23}, {"ruleId": "782", "severity": 1, "message": "804", "line": 43, "column": 15, "nodeType": "784", "messageId": "785", "endLine": 43, "endColumn": 27}, {"ruleId": "782", "severity": 1, "message": "847", "line": 44, "column": 17, "nodeType": "784", "messageId": "785", "endLine": 44, "endColumn": 31}, {"ruleId": "825", "severity": 1, "message": "848", "line": 98, "column": 6, "nodeType": "827", "endLine": 98, "endColumn": 18, "suggestions": "849"}, {"ruleId": "782", "severity": 1, "message": "850", "line": 101, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 101, "endColumn": 27}, {"ruleId": "782", "severity": 1, "message": "797", "line": 8, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 8, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "798", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "801", "line": 10, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 10, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "841", "line": 23, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 23, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "842", "line": 24, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 24, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "802", "line": 25, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 25, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "816", "line": 29, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 29, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "806", "line": 39, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 39, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "804", "line": 43, "column": 15, "nodeType": "784", "messageId": "785", "endLine": 43, "endColumn": 27}, {"ruleId": "782", "severity": 1, "message": "851", "line": 44, "column": 14, "nodeType": "784", "messageId": "785", "endLine": 44, "endColumn": 25}, {"ruleId": "782", "severity": 1, "message": "852", "line": 50, "column": 69, "nodeType": "784", "messageId": "785", "endLine": 50, "endColumn": 76}, {"ruleId": "782", "severity": 1, "message": "853", "line": 79, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 79, "endColumn": 26}, {"ruleId": "825", "severity": 1, "message": "854", "line": 145, "column": 6, "nodeType": "827", "endLine": 145, "endColumn": 8, "suggestions": "855"}, {"ruleId": "782", "severity": 1, "message": "856", "line": 689, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 689, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "857", "line": 20, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 20, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "858", "line": 21, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 21, "endColumn": 9}, {"ruleId": "782", "severity": 1, "message": "859", "line": 22, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 22, "endColumn": 11}, {"ruleId": "782", "severity": 1, "message": "796", "line": 23, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 23, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "860", "line": 26, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 26, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "861", "line": 69, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 69, "endColumn": 22}, {"ruleId": "862", "severity": 1, "message": "863", "line": 466, "column": 9, "nodeType": "864", "messageId": "865", "endLine": 469, "endColumn": 10}, {"ruleId": "825", "severity": 1, "message": "866", "line": 95, "column": 6, "nodeType": "827", "endLine": 95, "endColumn": 21, "suggestions": "867", "suppressions": "868"}, {"ruleId": "778", "severity": 1, "message": "779", "line": 260, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 264, "endColumn": 11}, {"ruleId": "778", "severity": 1, "message": "779", "line": 274, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 274, "endColumn": 70}, {"ruleId": "778", "severity": 1, "message": "779", "line": 278, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 278, "endColumn": 54}, {"ruleId": "778", "severity": 1, "message": "779", "line": 333, "column": 11, "nodeType": "780", "messageId": "781", "endLine": 338, "endColumn": 13}, {"ruleId": "778", "severity": 1, "message": "779", "line": 435, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 439, "endColumn": 11}, {"ruleId": "778", "severity": 1, "message": "779", "line": 451, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 451, "endColumn": 54}, {"ruleId": "778", "severity": 1, "message": "779", "line": 668, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 668, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 677, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 677, "endColumn": 70}, {"ruleId": "778", "severity": 1, "message": "779", "line": 681, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 681, "endColumn": 54}, {"ruleId": "782", "severity": 1, "message": "869", "line": 755, "column": 17, "nodeType": "784", "messageId": "785", "endLine": 755, "endColumn": 22}, {"ruleId": "778", "severity": 1, "message": "779", "line": 775, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 779, "endColumn": 11}, {"ruleId": "778", "severity": 1, "message": "779", "line": 794, "column": 11, "nodeType": "780", "messageId": "781", "endLine": 798, "endColumn": 13}, {"ruleId": "778", "severity": 1, "message": "779", "line": 801, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 804, "endColumn": 11}, {"ruleId": "778", "severity": 1, "message": "779", "line": 810, "column": 11, "nodeType": "780", "messageId": "781", "endLine": 814, "endColumn": 13}, {"ruleId": "778", "severity": 1, "message": "779", "line": 817, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 820, "endColumn": 11}, {"ruleId": "778", "severity": 1, "message": "779", "line": 885, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 889, "endColumn": 11}, {"ruleId": "870", "severity": 1, "message": "871", "line": 955, "column": 3, "nodeType": "872", "messageId": "873", "endLine": 955, "endColumn": 29}, {"ruleId": "870", "severity": 1, "message": "874", "line": 1143, "column": 3, "nodeType": "872", "messageId": "873", "endLine": 1143, "endColumn": 23}, {"ruleId": "870", "severity": 1, "message": "875", "line": 1238, "column": 3, "nodeType": "872", "messageId": "873", "endLine": 1238, "endColumn": 20}, {"ruleId": "778", "severity": 1, "message": "779", "line": 1287, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 1287, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 1317, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 1317, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 1370, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 1370, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 1412, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 1412, "endColumn": 163}, {"ruleId": "782", "severity": 1, "message": "876", "line": 6, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 6, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "802", "line": 11, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 11, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "838", "line": 1, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "839", "line": 5, "column": 7, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "877", "line": 3, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 3, "endColumn": 11}, {"ruleId": "782", "severity": 1, "message": "878", "line": 4, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 4, "endColumn": 6}, {"ruleId": "782", "severity": 1, "message": "879", "line": 5, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "880", "line": 6, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 6, "endColumn": 11}, {"ruleId": "782", "severity": 1, "message": "881", "line": 7, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 7, "endColumn": 6}, {"ruleId": "782", "severity": 1, "message": "882", "line": 12, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 12, "endColumn": 9}, {"ruleId": "782", "severity": 1, "message": "883", "line": 36, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 36, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "884", "line": 50, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 50, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "885", "line": 64, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 64, "endColumn": 20}, {"ruleId": "782", "severity": 1, "message": "886", "line": 88, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 88, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "887", "line": 104, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 104, "endColumn": 30}, {"ruleId": "782", "severity": 1, "message": "888", "line": 3, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 3, "endColumn": 12}, {"ruleId": "782", "severity": 1, "message": "880", "line": 3, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 3, "endColumn": 11}, {"ruleId": "782", "severity": 1, "message": "881", "line": 4, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 4, "endColumn": 6}, {"ruleId": "782", "severity": 1, "message": "889", "line": 5, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "890", "line": 6, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 6, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "891", "line": 7, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 7, "endColumn": 16}, {"ruleId": "782", "severity": 1, "message": "892", "line": 8, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 8, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "882", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 9}, {"ruleId": "782", "severity": 1, "message": "893", "line": 10, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 10, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "877", "line": 11, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 11, "endColumn": 11}, {"ruleId": "782", "severity": 1, "message": "878", "line": 12, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 12, "endColumn": 6}, {"ruleId": "782", "severity": 1, "message": "879", "line": 13, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 13, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "894", "line": 14, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 14, "endColumn": 16}, {"ruleId": "782", "severity": 1, "message": "895", "line": 15, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 15, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "888", "line": 16, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 16, "endColumn": 12}, {"ruleId": "782", "severity": 1, "message": "896", "line": 18, "column": 40, "nodeType": "784", "messageId": "785", "endLine": 18, "endColumn": 44}, {"ruleId": "782", "severity": 1, "message": "897", "line": 47, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 47, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "898", "line": 64, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 64, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "899", "line": 71, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 71, "endColumn": 20}, {"ruleId": "782", "severity": 1, "message": "886", "line": 79, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 79, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "887", "line": 95, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 95, "endColumn": 30}, {"ruleId": "782", "severity": 1, "message": "900", "line": 299, "column": 27, "nodeType": "784", "messageId": "785", "endLine": 299, "endColumn": 37}, {"ruleId": "782", "severity": 1, "message": "901", "line": 300, "column": 27, "nodeType": "784", "messageId": "785", "endLine": 300, "endColumn": 36}, {"ruleId": "782", "severity": 1, "message": "813", "line": 3, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 3, "endColumn": 8}, {"ruleId": "825", "severity": 1, "message": "902", "line": 54, "column": 6, "nodeType": "827", "endLine": 54, "endColumn": 34, "suggestions": "903"}, {"ruleId": "782", "severity": 1, "message": "904", "line": 25, "column": 13, "nodeType": "784", "messageId": "785", "endLine": 25, "endColumn": 25}, {"ruleId": "782", "severity": 1, "message": "905", "line": 33, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 33, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "906", "line": 34, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 34, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "907", "line": 35, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 35, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "908", "line": 36, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 36, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "909", "line": 37, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 37, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "910", "line": 41, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 41, "endColumn": 20}, {"ruleId": "782", "severity": 1, "message": "911", "line": 43, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 43, "endColumn": 34}, {"ruleId": "782", "severity": 1, "message": "912", "line": 69, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 69, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "913", "line": 69, "column": 19, "nodeType": "784", "messageId": "785", "endLine": 69, "endColumn": 29}, {"ruleId": "825", "severity": 1, "message": "914", "line": 88, "column": 6, "nodeType": "827", "endLine": 88, "endColumn": 18, "suggestions": "915"}, {"ruleId": "825", "severity": 1, "message": "916", "line": 448, "column": 6, "nodeType": "827", "endLine": 448, "endColumn": 28, "suggestions": "917"}, {"ruleId": "782", "severity": 1, "message": "918", "line": 4, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 4, "endColumn": 12}, {"ruleId": "782", "severity": 1, "message": "919", "line": 8, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 8, "endColumn": 7}, {"ruleId": "782", "severity": 1, "message": "920", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 11}, {"ruleId": "782", "severity": 1, "message": "921", "line": 10, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 10, "endColumn": 15}, {"ruleId": "825", "severity": 1, "message": "902", "line": 46, "column": 6, "nodeType": "827", "endLine": 46, "endColumn": 18, "suggestions": "922"}, {"ruleId": "782", "severity": 1, "message": "923", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "857", "line": 10, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 10, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "858", "line": 11, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 11, "endColumn": 9}, {"ruleId": "782", "severity": 1, "message": "859", "line": 12, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 12, "endColumn": 11}, {"ruleId": "782", "severity": 1, "message": "921", "line": 33, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 33, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "924", "line": 35, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 35, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "851", "line": 42, "column": 14, "nodeType": "784", "messageId": "785", "endLine": 42, "endColumn": 25}, {"ruleId": "782", "severity": 1, "message": "905", "line": 52, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 52, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "906", "line": 53, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 53, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "907", "line": 54, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 54, "endColumn": 22}, {"ruleId": "782", "severity": 1, "message": "908", "line": 55, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 55, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "909", "line": 56, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 56, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "925", "line": 57, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 57, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "926", "line": 58, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 58, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "927", "line": 59, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 59, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "820", "line": 72, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 72, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "928", "line": 79, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 79, "endColumn": 23}, {"ruleId": "782", "severity": 1, "message": "929", "line": 79, "column": 25, "nodeType": "784", "messageId": "785", "endLine": 79, "endColumn": 41}, {"ruleId": "782", "severity": 1, "message": "930", "line": 80, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 80, "endColumn": 27}, {"ruleId": "782", "severity": 1, "message": "931", "line": 85, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 85, "endColumn": 26}, {"ruleId": "825", "severity": 1, "message": "902", "line": 105, "column": 6, "nodeType": "827", "endLine": 105, "endColumn": 18, "suggestions": "932"}, {"ruleId": "825", "severity": 1, "message": "933", "line": 112, "column": 6, "nodeType": "827", "endLine": 112, "endColumn": 20, "suggestions": "934"}, {"ruleId": "825", "severity": 1, "message": "935", "line": 127, "column": 6, "nodeType": "827", "endLine": 127, "endColumn": 34, "suggestions": "936"}, {"ruleId": "782", "severity": 1, "message": "937", "line": 283, "column": 13, "nodeType": "784", "messageId": "785", "endLine": 283, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "860", "line": 17, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 17, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "921", "line": 34, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 34, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "924", "line": 35, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 35, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "938", "line": 39, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 39, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "905", "line": 51, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 51, "endColumn": 15}, {"ruleId": "782", "severity": 1, "message": "906", "line": 52, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 52, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "908", "line": 54, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 54, "endColumn": 21}, {"ruleId": "782", "severity": 1, "message": "909", "line": 55, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 55, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "911", "line": 62, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 62, "endColumn": 34}, {"ruleId": "782", "severity": 1, "message": "939", "line": 105, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 105, "endColumn": 26}, {"ruleId": "782", "severity": 1, "message": "940", "line": 105, "column": 28, "nodeType": "784", "messageId": "785", "endLine": 105, "endColumn": 47}, {"ruleId": "825", "severity": 1, "message": "933", "line": 145, "column": 6, "nodeType": "827", "endLine": 145, "endColumn": 18, "suggestions": "941"}, {"ruleId": "782", "severity": 1, "message": "942", "line": 701, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 701, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "943", "line": 1311, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 1311, "endColumn": 28}, {"ruleId": "782", "severity": 1, "message": "944", "line": 1316, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 1316, "endColumn": 30}, {"ruleId": "782", "severity": 1, "message": "945", "line": 1883, "column": 9, "nodeType": "784", "messageId": "785", "endLine": 1883, "endColumn": 23}, {"ruleId": "782", "severity": 1, "message": "838", "line": 1, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "839", "line": 5, "column": 7, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "838", "line": 1, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "839", "line": 5, "column": 7, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "838", "line": 1, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "839", "line": 5, "column": 7, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "838", "line": 1, "column": 8, "nodeType": "784", "messageId": "785", "endLine": 1, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "839", "line": 5, "column": 7, "nodeType": "784", "messageId": "785", "endLine": 5, "endColumn": 14}, {"ruleId": "782", "severity": 1, "message": "946", "line": 83, "column": 13, "nodeType": "784", "messageId": "785", "endLine": 83, "endColumn": 21}, {"ruleId": "778", "severity": 1, "message": "779", "line": 109, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 109, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 123, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 123, "endColumn": 70}, {"ruleId": "778", "severity": 1, "message": "779", "line": 127, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 127, "endColumn": 54}, {"ruleId": "778", "severity": 1, "message": "779", "line": 212, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 212, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 226, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 226, "endColumn": 70}, {"ruleId": "778", "severity": 1, "message": "779", "line": 230, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 230, "endColumn": 54}, {"ruleId": "778", "severity": 1, "message": "779", "line": 271, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 271, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 280, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 280, "endColumn": 70}, {"ruleId": "778", "severity": 1, "message": "779", "line": 284, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 284, "endColumn": 54}, {"ruleId": "778", "severity": 1, "message": "779", "line": 320, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 320, "endColumn": 70}, {"ruleId": "778", "severity": 1, "message": "779", "line": 324, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 324, "endColumn": 54}, {"ruleId": "778", "severity": 1, "message": "779", "line": 360, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 360, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 369, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 369, "endColumn": 70}, {"ruleId": "778", "severity": 1, "message": "779", "line": 373, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 373, "endColumn": 54}, {"ruleId": "778", "severity": 1, "message": "779", "line": 450, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 450, "endColumn": 163}, {"ruleId": "778", "severity": 1, "message": "779", "line": 459, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 459, "endColumn": 70}, {"ruleId": "778", "severity": 1, "message": "779", "line": 463, "column": 9, "nodeType": "780", "messageId": "781", "endLine": 463, "endColumn": 54}, {"ruleId": "782", "severity": 1, "message": "947", "line": 12, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 12, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "805", "line": 27, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 27, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "818", "line": 30, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 30, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "907", "line": 34, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 34, "endColumn": 29}, {"ruleId": "782", "severity": 1, "message": "912", "line": 49, "column": 10, "nodeType": "784", "messageId": "785", "endLine": 49, "endColumn": 17}, {"ruleId": "782", "severity": 1, "message": "913", "line": 49, "column": 19, "nodeType": "784", "messageId": "785", "endLine": 49, "endColumn": 29}, {"ruleId": "825", "severity": 1, "message": "902", "line": 64, "column": 6, "nodeType": "827", "endLine": 64, "endColumn": 32, "suggestions": "948"}, {"ruleId": "782", "severity": 1, "message": "949", "line": 270, "column": 17, "nodeType": "784", "messageId": "785", "endLine": 270, "endColumn": 23}, {"ruleId": "782", "severity": 1, "message": "950", "line": 17, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 17, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "859", "line": 16, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 16, "endColumn": 11}, {"ruleId": "782", "severity": 1, "message": "858", "line": 17, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 17, "endColumn": 9}, {"ruleId": "782", "severity": 1, "message": "857", "line": 19, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 19, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "818", "line": 14, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 14, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "816", "line": 12, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 12, "endColumn": 13}, {"ruleId": "782", "severity": 1, "message": "951", "line": 3, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 3, "endColumn": 6}, {"ruleId": "782", "severity": 1, "message": "802", "line": 9, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 9, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "802", "line": 33, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 33, "endColumn": 10}, {"ruleId": "782", "severity": 1, "message": "952", "line": 37, "column": 3, "nodeType": "784", "messageId": "785", "endLine": 37, "endColumn": 8}, {"ruleId": "782", "severity": 1, "message": "953", "line": 42, "column": 17, "nodeType": "784", "messageId": "785", "endLine": 42, "endColumn": 27}, {"ruleId": "782", "severity": 1, "message": "904", "line": 44, "column": 15, "nodeType": "784", "messageId": "785", "endLine": 44, "endColumn": 27}, {"ruleId": "782", "severity": 1, "message": "806", "line": 47, "column": 11, "nodeType": "784", "messageId": "785", "endLine": 47, "endColumn": 19}, {"ruleId": "782", "severity": 1, "message": "843", "line": 50, "column": 12, "nodeType": "784", "messageId": "785", "endLine": 50, "endColumn": 21}, {"ruleId": "825", "severity": 1, "message": "954", "line": 100, "column": 6, "nodeType": "827", "endLine": 100, "endColumn": 18, "suggestions": "955"}, {"ruleId": "825", "severity": 1, "message": "956", "line": 105, "column": 6, "nodeType": "827", "endLine": 105, "endColumn": 33, "suggestions": "957"}, {"ruleId": "825", "severity": 1, "message": "958", "line": 110, "column": 6, "nodeType": "827", "endLine": 110, "endColumn": 34, "suggestions": "959"}, "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "no-unused-vars", "'Avatar' is defined but never used.", "Identifier", "unusedVar", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'CardActions' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'Paper' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'IconButton' is defined but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["960"], "'handleOpenDetails' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'RefreshIcon' is defined but never used.", "'Button' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["961"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["962"], "'SearchIcon' is defined but never used.", "'AssignmentIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array.", ["963"], "'handleOptionSelect' is assigned a value but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["964"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["965"], ["966"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["967"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["968"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["969"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", ["970"], "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["971"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["972"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["973"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["974"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["975"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'Box' is defined but never used.", "'Badge' is defined but never used.", "'FilterIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["976"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["977"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["978"], {"desc": "979", "fix": "980"}, {"desc": "981", "fix": "982"}, {"desc": "983", "fix": "984"}, {"desc": "985", "fix": "986"}, {"desc": "987", "fix": "988"}, {"desc": "989", "fix": "990"}, {"kind": "991", "justification": "992"}, {"desc": "993", "fix": "994"}, {"desc": "995", "fix": "996"}, {"desc": "997", "fix": "998"}, {"desc": "999", "fix": "1000"}, {"desc": "999", "fix": "1001"}, {"desc": "1002", "fix": "1003"}, {"desc": "1004", "fix": "1005"}, {"desc": "1006", "fix": "1007"}, {"desc": "1008", "fix": "1009"}, {"desc": "1010", "fix": "1011"}, {"desc": "1012", "fix": "1013"}, {"desc": "1014", "fix": "1015"}, "Update the dependencies array to be: [error, filters, user]", {"range": "1016", "text": "1017"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1018", "text": "1019"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "1020", "text": "1021"}, "Update the dependencies array to be: [cantiereId, loadComande]", {"range": "1022", "text": "1023"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1024", "text": "1025"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "1026", "text": "1027"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1028", "text": "1029"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1030", "text": "1031"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1032", "text": "1033"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1034", "text": "1035"}, {"range": "1036", "text": "1035"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1037", "text": "1038"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1039", "text": "1040"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1041", "text": "1042"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1043", "text": "1044"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1045", "text": "1046"}, "Update the dependencies array to be: [cavi, searchTerm, filters, filterCavi]", {"range": "1047", "text": "1048"}, "Update the dependencies array to be: [certificazioni, filterCertificazioni, searchTerm]", {"range": "1049", "text": "1050"}, [19729, 19738], "[error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [2335, 2347], "[cantiereId, loadComande]", [4642, 4644], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1014, 1026], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]", [2371, 2383], "[cantiereId, loadInitialData]", [2468, 2495], "[cavi, searchTerm, filters, filterCavi]", [2579, 2607], "[certificazioni, filterCertificazioni, searchTerm]"]